<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class VideoDownload extends Model
{
    use HasFactory;

    protected $fillable = [
        'video_id',
        'ip_address',
        'user_agent',
        'country',
        'city',
    ];

    /**
     * Get the video that was downloaded.
     */
    public function video()
    {
        return $this->belongsTo(Video::class);
    }
}
