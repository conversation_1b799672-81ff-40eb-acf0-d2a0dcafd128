<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Video;
use App\Models\VideoDownload;
use App\Models\VideoLike;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class VideoController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth')->except(['download', 'like']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $videos = Video::with('user')
            ->where('user_id', Auth::id())
            ->latest()
            ->paginate(10);

        return view('videos.index', compact('videos'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('videos.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'video' => 'required|file|mimes:mp4,mov,avi,wmv|max:51200', // 50MB max
        ]);

        $videoFile = $request->file('video');
        $videoPath = $videoFile->store('videos', 'public');

        // Get video duration and file size
        $filePath = storage_path('app/public/' . $videoPath);
        $fileSize = $videoFile->getSize();

        // Create video record
        $video = Video::create([
            'user_id' => Auth::id(),
            'title' => $request->title,
            'description' => $request->description,
            'video_path' => $videoPath,
            'file_size' => $fileSize,
        ]);

        // Generate thumbnail (simplified - you can enhance this with ffmpeg)
        $this->generateThumbnail($video);

        return redirect()->route('videos.index')
            ->with('success', 'Video uploaded successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Video $video)
    {
        $video->load('user');
        return view('videos.show', compact('video'));
    }

    /**
     * Download video and track statistics.
     */
    public function download(Video $video, Request $request)
    {
        // Track download
        VideoDownload::create([
            'video_id' => $video->id,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        // Increment download count
        $video->increment('downloads_count');

        // Return file download
        $filePath = storage_path('app/public/' . $video->video_path);

        if (!file_exists($filePath)) {
            abort(404, 'Video file not found');
        }

        return response()->download($filePath, $video->title . '.mp4');
    }

    /**
     * Like/unlike video.
     */
    public function like(Video $video, Request $request)
    {
        $ipAddress = $request->ip();

        $existingLike = VideoLike::where('video_id', $video->id)
            ->where('ip_address', $ipAddress)
            ->first();

        if ($existingLike) {
            // Unlike
            $existingLike->delete();
            $video->decrement('likes_count');
            $liked = false;
        } else {
            // Like
            VideoLike::create([
                'video_id' => $video->id,
                'ip_address' => $ipAddress,
            ]);
            $video->increment('likes_count');
            $liked = true;
        }

        return response()->json([
            'liked' => $liked,
            'likes_count' => $video->fresh()->likes_count,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Video $video)
    {
        // Check if user owns the video or is admin
        if ($video->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            abort(403, 'Unauthorized');
        }

        // Delete files
        if ($video->video_path) {
            Storage::disk('public')->delete($video->video_path);
        }
        if ($video->thumbnail_path) {
            Storage::disk('public')->delete($video->thumbnail_path);
        }

        // Delete video record
        $video->delete();

        return redirect()->back()
            ->with('success', 'Video deleted successfully!');
    }

    /**
     * Generate thumbnail for video (simplified version).
     */
    private function generateThumbnail(Video $video)
    {
        // This is a simplified version. In production, you'd use ffmpeg
        // For now, we'll create a placeholder thumbnail
        $manager = new ImageManager(new Driver());

        // Create a simple placeholder image
        $image = $manager->create(320, 240)->fill('000000');

        // Add text
        $thumbnailPath = 'thumbnails/' . $video->id . '.jpg';
        $fullPath = storage_path('app/public/' . $thumbnailPath);

        // Ensure directory exists
        $directory = dirname($fullPath);
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        $image->save($fullPath);

        // Update video with thumbnail path
        $video->update(['thumbnail_path' => $thumbnailPath]);
    }

    /**
     * Get trending videos (API).
     */
    public function trending()
    {
        $videos = Video::with('user')
            ->active()
            ->orderByDesc('likes_count')
            ->orderByDesc('downloads_count')
            ->take(20)
            ->get();

        return response()->json($videos);
    }

    /**
     * Get recent videos (API).
     */
    public function recent()
    {
        $videos = Video::with('user')
            ->active()
            ->latest()
            ->take(20)
            ->get();

        return response()->json($videos);
    }

    /**
     * Get user's videos (API).
     */
    public function userVideos()
    {
        $videos = Video::with('user')
            ->where('user_id', Auth::id())
            ->latest()
            ->get();

        return response()->json($videos);
    }
}
