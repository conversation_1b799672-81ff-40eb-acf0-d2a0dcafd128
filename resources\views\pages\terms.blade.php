@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h1 class="text-2xl font-bold text-gray-900">Terms of Service</h1>
            <p class="mt-1 text-sm text-gray-600">Last updated: {{ date('F j, Y') }}</p>
        </div>

        <div class="px-6 py-8 prose prose-gray max-w-none">
            <h2>1. Acceptance of Terms</h2>
            <p>
                By accessing and using StoryGue ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. 
                If you do not agree to abide by the above, please do not use this service.
            </p>

            <h2>2. Description of Service</h2>
            <p>
                StoryGue is a video sharing platform that allows users to upload, share, and download short videos. 
                The service is provided free of charge for personal, non-commercial use.
            </p>

            <h2>3. User Accounts</h2>
            <p>
                To access certain features of the Service, you may be required to create an account. You are responsible for:
            </p>
            <ul>
                <li>Maintaining the confidentiality of your account credentials</li>
                <li>All activities that occur under your account</li>
                <li>Providing accurate and complete information</li>
                <li>Updating your information to keep it current</li>
            </ul>

            <h2>4. Content Guidelines</h2>
            <p>
                Users are responsible for the content they upload. By uploading content, you represent and warrant that:
            </p>
            <ul>
                <li>You own or have the necessary rights to the content</li>
                <li>The content does not violate any third-party rights</li>
                <li>The content is not illegal, harmful, or offensive</li>
                <li>The content complies with all applicable laws and regulations</li>
            </ul>

            <h2>5. Prohibited Content</h2>
            <p>The following types of content are strictly prohibited:</p>
            <ul>
                <li>Copyrighted material without proper authorization</li>
                <li>Adult content or sexually explicit material</li>
                <li>Violence, harassment, or hate speech</li>
                <li>Spam or misleading content</li>
                <li>Content that promotes illegal activities</li>
                <li>Malicious software or harmful code</li>
            </ul>

            <h2>6. User Conduct</h2>
            <p>Users agree not to:</p>
            <ul>
                <li>Use the Service for any unlawful purpose</li>
                <li>Interfere with or disrupt the Service</li>
                <li>Attempt to gain unauthorized access to the Service</li>
                <li>Impersonate any person or entity</li>
                <li>Collect or harvest personal information from other users</li>
            </ul>

            <h2>7. Intellectual Property</h2>
            <p>
                The Service and its original content, features, and functionality are owned by StoryGue and are protected by 
                international copyright, trademark, patent, trade secret, and other intellectual property laws.
            </p>

            <h2>8. Content License</h2>
            <p>
                By uploading content to the Service, you grant StoryGue a non-exclusive, worldwide, royalty-free license to 
                use, display, reproduce, and distribute your content in connection with the Service.
            </p>

            <h2>9. Privacy</h2>
            <p>
                Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service, 
                to understand our practices.
            </p>

            <h2>10. Termination</h2>
            <p>
                We may terminate or suspend your account and access to the Service immediately, without prior notice or liability, 
                for any reason, including if you breach the Terms of Service.
            </p>

            <h2>11. Disclaimer</h2>
            <p>
                The Service is provided on an "AS IS" and "AS AVAILABLE" basis. StoryGue makes no warranties, expressed or implied, 
                and hereby disclaims all other warranties including implied warranties of merchantability, fitness for a particular purpose, 
                or non-infringement.
            </p>

            <h2>12. Limitation of Liability</h2>
            <p>
                In no event shall StoryGue be liable for any indirect, incidental, special, consequential, or punitive damages, 
                including without limitation, loss of profits, data, use, goodwill, or other intangible losses.
            </p>

            <h2>13. Changes to Terms</h2>
            <p>
                We reserve the right to modify or replace these Terms at any time. If a revision is material, we will try to provide 
                at least 30 days notice prior to any new terms taking effect.
            </p>

            <h2>14. Contact Information</h2>
            <p>
                If you have any questions about these Terms of Service, please contact us at:
            </p>
            <ul>
                <li>Email: <EMAIL></li>
                <li>Address: StoryGue Support Team</li>
            </ul>

            <div class="mt-8 pt-6 border-t border-gray-200">
                <p class="text-sm text-gray-500">
                    By using StoryGue, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service.
                </p>
            </div>
        </div>
    </div>

    <!-- Back Button -->
    <div class="mt-6">
        <a href="{{ route('home') }}" 
           class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            <svg class="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            Back to Home
        </a>
    </div>
</div>
@endsection
