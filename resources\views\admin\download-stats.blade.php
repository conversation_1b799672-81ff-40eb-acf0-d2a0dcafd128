@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Download Statistics</h1>
            <p class="mt-1 text-sm text-gray-600">Analyze download patterns and user behavior</p>
        </div>
        <a href="{{ route('admin.dashboard') }}" 
           class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            <svg class="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            Back to Dashboard
        </a>
    </div>

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <!-- Top Countries -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Top Countries</h3>
                @if($topCountries->count() > 0)
                    <div class="space-y-3">
                        @foreach($topCountries->take(5) as $country)
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ $country->country ?: 'Unknown' }}
                                    </div>
                                </div>
                                <div class="text-sm text-gray-500">
                                    {{ number_format($country->downloads) }} downloads
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-sm text-gray-500">No location data available yet.</p>
                @endif
            </div>
        </div>

        <!-- Downloads by Hour -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Peak Hours</h3>
                @if($downloadsByHour->count() > 0)
                    <div class="space-y-2">
                        @php
                            $maxDownloads = $downloadsByHour->max('downloads');
                        @endphp
                        @foreach($downloadsByHour->sortByDesc('downloads')->take(5) as $hourData)
                            <div class="flex items-center justify-between">
                                <div class="text-sm font-medium text-gray-900">
                                    {{ sprintf('%02d:00', $hourData->hour) }}
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-16 bg-gray-200 rounded-full h-2">
                                        <div class="bg-black h-2 rounded-full" 
                                             style="width: {{ $maxDownloads > 0 ? ($hourData->downloads / $maxDownloads) * 100 : 0 }}%"></div>
                                    </div>
                                    <span class="text-sm text-gray-500">{{ $hourData->downloads }}</span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-sm text-gray-500">No download data available yet.</p>
                @endif
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="text-sm font-medium text-gray-900">Total Downloads</div>
                        <div class="text-lg font-bold text-gray-900">{{ number_format($recentDownloads->count()) }}</div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-sm font-medium text-gray-900">Today</div>
                        <div class="text-sm text-gray-500">
                            {{ number_format($recentDownloads->where('created_at', '>=', now()->startOfDay())->count()) }}
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-sm font-medium text-gray-900">This Week</div>
                        <div class="text-sm text-gray-500">
                            {{ number_format($recentDownloads->where('created_at', '>=', now()->startOfWeek())->count()) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Downloads Table -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Downloads</h3>
            
            @if($recentDownloads->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Video
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    User
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    IP Address
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Location
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Time
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($recentDownloads->take(20) as $download)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-16 bg-gray-200 rounded overflow-hidden">
                                                @if($download->video->thumbnail_path)
                                                    <img src="{{ Storage::url($download->video->thumbnail_path) }}" 
                                                         alt="{{ $download->video->title }}" 
                                                         class="h-full w-full object-cover">
                                                @else
                                                    <div class="h-full w-full flex items-center justify-center bg-gray-300">
                                                        <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                                                        </svg>
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="ml-3">
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ Str::limit($download->video->title, 30) }}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $download->video->user->name }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500 font-mono">{{ $download->ip_address }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">
                                            @if($download->city && $download->country)
                                                {{ $download->city }}, {{ $download->country }}
                                            @elseif($download->country)
                                                {{ $download->country }}
                                            @else
                                                Unknown
                                            @endif
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">
                                            {{ $download->created_at->format('M j, Y H:i') }}
                                        </div>
                                        <div class="text-xs text-gray-400">
                                            {{ $download->created_at->diffForHumans() }}
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                @if($recentDownloads->count() > 20)
                    <div class="mt-4 text-center">
                        <p class="text-sm text-gray-500">
                            Showing 20 of {{ number_format($recentDownloads->count()) }} recent downloads
                        </p>
                    </div>
                @endif
            @else
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No downloads yet</h3>
                    <p class="mt-1 text-sm text-gray-500">Download statistics will appear here once users start downloading videos.</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
