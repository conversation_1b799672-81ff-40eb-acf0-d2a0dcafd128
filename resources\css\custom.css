/* StoryGue Custom Styles */

/* Logo Animation */
.logo-bounce {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Video Card Hover Effects */
.video-card {
    transition: all 0.3s ease;
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Button Animations */
.btn-primary {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

/* Loading Spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #000;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Upload Area Animation */
.upload-area {
    transition: all 0.3s ease;
}

.upload-area.dragover {
    border-color: #000;
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(1.02);
}

/* Progress Bar */
.progress-bar {
    background: linear-gradient(90deg, #000 0%, #333 100%);
    animation: progress 2s ease-in-out;
}

@keyframes progress {
    0% { width: 0%; }
    100% { width: 100%; }
}

/* Notification Animations */
.notification {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Video Player Enhancements */
.video-player {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    padding: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-player:hover .video-controls {
    opacity: 1;
}

/* Like Button Animation */
.like-btn {
    transition: all 0.2s ease;
}

.like-btn:hover {
    transform: scale(1.1);
}

.like-btn.liked {
    color: #ef4444;
    animation: heartbeat 0.6s ease-in-out;
}

@keyframes heartbeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1); }
    75% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Mobile Responsive Enhancements */
@media (max-width: 768px) {
    .video-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1rem;
    }
    
    .mobile-menu {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .mobile-menu.open {
        transform: translateX(0);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .dark-mode .bg-white {
        background-color: #2d2d2d;
    }
    
    .dark-mode .text-gray-900 {
        color: #ffffff;
    }
    
    .dark-mode .border-gray-200 {
        border-color: #404040;
    }
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Tooltip */
.tooltip {
    position: relative;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #000;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
}

.tooltip:hover::after {
    opacity: 1;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus States */
.focus-ring:focus {
    outline: 2px solid #000;
    outline-offset: 2px;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .high-contrast {
        border: 2px solid #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Custom Utilities */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.aspect-video {
    aspect-ratio: 16 / 9;
}

.backdrop-blur {
    backdrop-filter: blur(10px);
}

/* StoryGue Brand Colors */
:root {
    --sg-black: #000000;
    --sg-white: #ffffff;
    --sg-gray-50: #f9fafb;
    --sg-gray-100: #f3f4f6;
    --sg-gray-200: #e5e7eb;
    --sg-gray-300: #d1d5db;
    --sg-gray-400: #9ca3af;
    --sg-gray-500: #6b7280;
    --sg-gray-600: #4b5563;
    --sg-gray-700: #374151;
    --sg-gray-800: #1f2937;
    --sg-gray-900: #111827;
}

/* Brand Gradient */
.bg-gradient-sg {
    background: linear-gradient(135deg, var(--sg-black) 0%, var(--sg-gray-800) 100%);
}

/* Glassmorphism Effect */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}
