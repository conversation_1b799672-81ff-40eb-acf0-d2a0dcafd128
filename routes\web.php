<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\VideoController;
use App\Http\Controllers\AdminController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// ============================================================================
// PUBLIC ROUTES (No Authentication Required)
// ============================================================================

// Home page - Display all active videos
Route::get('/', [HomeController::class, 'index'])->name('home');

// Static/Legal pages
Route::view('/terms', 'pages.terms')->name('terms');
Route::view('/privacy', 'pages.privacy')->name('privacy');

// Public video actions (no auth required)
Route::get('/videos/{video}/download', [VideoController::class, 'download'])->name('videos.download');
Route::post('/videos/{video}/like', [VideoController::class, 'like'])->name('videos.like');

// ============================================================================
// AUTHENTICATION ROUTES
// ============================================================================

Auth::routes([
    'register' => true,
    'reset' => true,
    'verify' => false,
]);

// ============================================================================
// AUTHENTICATED USER ROUTES
// ============================================================================

Route::middleware(['auth'])->group(function () {

    // Video Management Routes
    Route::prefix('videos')->name('videos.')->group(function () {
        Route::get('/', [VideoController::class, 'index'])->name('index');           // My Videos
        Route::get('/create', [VideoController::class, 'create'])->name('create');   // Upload Form
        Route::post('/', [VideoController::class, 'store'])->name('store');          // Store Video
        Route::get('/{video}', [VideoController::class, 'show'])->name('show');      // View Video
        Route::delete('/{video}', [VideoController::class, 'destroy'])->name('destroy'); // Delete Video
    });

    // User Dashboard/Profile Routes (if needed in future)
    // Route::get('/dashboard', [UserController::class, 'dashboard'])->name('dashboard');
    // Route::get('/profile', [UserController::class, 'profile'])->name('profile');

});

// ============================================================================
// ADMIN ROUTES (Requires Authentication + Admin Role)
// ============================================================================

Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {

    // Admin Dashboard
    Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard.alt');

    // Video Management
    Route::prefix('videos')->name('videos.')->group(function () {
        Route::get('/', [AdminController::class, 'videos'])->name('index');
        Route::patch('/{video}/toggle-status', [AdminController::class, 'toggleVideoStatus'])->name('toggle-status');
        Route::delete('/{video}', [AdminController::class, 'deleteVideo'])->name('delete');
    });

    // User Management
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [AdminController::class, 'users'])->name('index');
        Route::patch('/{user}/toggle-admin', [AdminController::class, 'toggleUserAdmin'])->name('toggle-admin');
        Route::patch('/{user}/toggle-status', [AdminController::class, 'toggleUserStatus'])->name('toggle-status');
    });

    // Statistics & Analytics
    Route::prefix('stats')->name('stats.')->group(function () {
        Route::get('/downloads', [AdminController::class, 'downloadStats'])->name('downloads');
        Route::get('/analytics', [AdminController::class, 'analytics'])->name('analytics');
        Route::get('/reports', [AdminController::class, 'reports'])->name('reports');
    });

});

// ============================================================================
// API ROUTES (for AJAX calls)
// ============================================================================

Route::prefix('api')->name('api.')->group(function () {

    // Public API endpoints
    Route::get('/videos/trending', [VideoController::class, 'trending'])->name('videos.trending');
    Route::get('/videos/recent', [VideoController::class, 'recent'])->name('videos.recent');

    // Authenticated API endpoints
    Route::middleware(['auth'])->group(function () {
        Route::post('/videos/{video}/like', [VideoController::class, 'like'])->name('videos.like');
        Route::get('/user/videos', [VideoController::class, 'userVideos'])->name('user.videos');
    });

    // Admin API endpoints
    Route::middleware(['auth', 'admin'])->group(function () {
        Route::get('/admin/stats/overview', [AdminController::class, 'statsOverview'])->name('admin.stats.overview');
        Route::get('/admin/stats/charts', [AdminController::class, 'statsCharts'])->name('admin.stats.charts');
    });

});

// ============================================================================
// FALLBACK ROUTES
// ============================================================================

// Handle 404 for undefined routes
Route::fallback(function () {
    return redirect()->route('home')->with('error', 'Page not found. Redirected to home.');
});
