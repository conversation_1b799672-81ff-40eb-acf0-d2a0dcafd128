<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\VideoController;
use App\Http\Controllers\AdminController;

// Public routes
Route::get('/', [HomeController::class, 'index'])->name('home');

// Authentication routes
Auth::routes();

// Video routes
Route::resource('videos', VideoController::class)->except(['edit', 'update']);
Route::get('/videos/{video}/download', [VideoController::class, 'download'])->name('videos.download');
Route::post('/videos/{video}/like', [VideoController::class, 'like'])->name('videos.like');

// Admin routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/videos', [AdminController::class, 'videos'])->name('videos');
    Route::patch('/videos/{video}/toggle-status', [AdminController::class, 'toggleVideoStatus'])->name('videos.toggle-status');
    Route::get('/users', [AdminController::class, 'users'])->name('users');
    Route::patch('/users/{user}/toggle-admin', [AdminController::class, 'toggleUserAdmin'])->name('users.toggle-admin');
    Route::get('/download-stats', [AdminController::class, 'downloadStats'])->name('download-stats');
});

// Static pages
Route::view('/terms', 'pages.terms')->name('terms');
Route::view('/privacy', 'pages.privacy')->name('privacy');
