<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Video;
use App\Models\VideoDownload;
use App\Models\VideoLike;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Show admin dashboard.
     */
    public function dashboard()
    {
        $stats = [
            'total_videos' => Video::count(),
            'total_users' => User::count(),
            'total_downloads' => VideoDownload::count(),
            'total_likes' => VideoLike::count(),
            'active_videos' => Video::where('is_active', true)->count(),
            'inactive_videos' => Video::where('is_active', false)->count(),
        ];

        // Recent videos
        $recentVideos = Video::with('user')
            ->latest()
            ->take(10)
            ->get();

        // Top videos by downloads
        $topVideosByDownloads = Video::with('user')
            ->orderBy('downloads_count', 'desc')
            ->take(10)
            ->get();

        // Top videos by likes
        $topVideosByLikes = Video::with('user')
            ->orderBy('likes_count', 'desc')
            ->take(10)
            ->get();

        // Daily stats for the last 30 days
        $dailyStats = VideoDownload::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as downloads')
            )
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return view('admin.dashboard', compact(
            'stats',
            'recentVideos',
            'topVideosByDownloads',
            'topVideosByLikes',
            'dailyStats'
        ));
    }

    /**
     * Show all videos for management.
     */
    public function videos(Request $request)
    {
        $query = Video::with('user');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('is_active', $request->status);
        }

        $videos = $query->latest()->paginate(20);

        return view('admin.videos', compact('videos'));
    }

    /**
     * Toggle video status (active/inactive).
     */
    public function toggleVideoStatus(Video $video)
    {
        $video->update(['is_active' => !$video->is_active]);

        $status = $video->is_active ? 'activated' : 'deactivated';

        return redirect()->back()
            ->with('success', "Video has been {$status} successfully!");
    }

    /**
     * Show users management.
     */
    public function users(Request $request)
    {
        $query = User::query();

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->withCount('videos')->latest()->paginate(20);

        return view('admin.users', compact('users'));
    }

    /**
     * Toggle user admin status.
     */
    public function toggleUserAdmin(User $user)
    {
        // Prevent removing admin from current user
        if ($user->id === auth()->id()) {
            return redirect()->back()
                ->with('error', 'You cannot remove admin privileges from yourself!');
        }

        $user->update(['is_admin' => !$user->is_admin]);

        $status = $user->is_admin ? 'granted' : 'removed';

        return redirect()->back()
            ->with('success', "Admin privileges have been {$status} for {$user->name}!");
    }

    /**
     * Show download statistics.
     */
    public function downloadStats()
    {
        // Top countries by downloads (if you implement geolocation)
        $topCountries = VideoDownload::select('country', DB::raw('COUNT(*) as downloads'))
            ->whereNotNull('country')
            ->groupBy('country')
            ->orderBy('downloads', 'desc')
            ->take(10)
            ->get();

        // Downloads by hour of day
        $downloadsByHour = VideoDownload::select(
                DB::raw('HOUR(created_at) as hour'),
                DB::raw('COUNT(*) as downloads')
            )
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();

        // Recent downloads
        $recentDownloads = VideoDownload::with('video.user')
            ->latest()
            ->take(50)
            ->get();

        return view('admin.download-stats', compact(
            'topCountries',
            'downloadsByHour',
            'recentDownloads'
        ));
    }

    /**
     * Delete video (Admin only).
     */
    public function deleteVideo(Video $video)
    {
        // Delete files
        if ($video->video_path) {
            Storage::disk('public')->delete($video->video_path);
        }
        if ($video->thumbnail_path) {
            Storage::disk('public')->delete($video->thumbnail_path);
        }

        // Delete video record
        $video->delete();

        return redirect()->back()->with('success', 'Video deleted successfully!');
    }

    /**
     * Toggle user status (active/inactive).
     */
    public function toggleUserStatus(User $user)
    {
        // Add is_active column if it doesn't exist
        $user->update([
            'email_verified_at' => $user->email_verified_at ? null : now()
        ]);

        $status = $user->email_verified_at ? 'activated' : 'deactivated';
        return redirect()->back()->with('success', "User {$status} successfully!");
    }

    /**
     * Get analytics data.
     */
    public function analytics()
    {
        $data = [
            'total_videos' => Video::count(),
            'total_users' => User::count(),
            'total_downloads' => VideoDownload::count(),
            'total_likes' => VideoLike::count(),
            'videos_this_month' => Video::whereMonth('created_at', now()->month)->count(),
            'users_this_month' => User::whereMonth('created_at', now()->month)->count(),
        ];

        return view('admin.analytics', compact('data'));
    }

    /**
     * Generate reports.
     */
    public function reports()
    {
        $reports = [
            'top_videos' => Video::withCount(['downloads', 'likes'])
                ->orderByDesc('downloads_count')
                ->take(10)
                ->get(),
            'active_users' => User::withCount('videos')
                ->having('videos_count', '>', 0)
                ->orderByDesc('videos_count')
                ->take(10)
                ->get(),
        ];

        return view('admin.reports', compact('reports'));
    }

    /**
     * API: Get stats overview.
     */
    public function statsOverview()
    {
        return response()->json([
            'total_videos' => Video::count(),
            'total_users' => User::count(),
            'total_downloads' => VideoDownload::count(),
            'total_likes' => VideoLike::count(),
            'active_videos' => Video::where('is_active', true)->count(),
            'admin_users' => User::where('is_admin', true)->count(),
        ]);
    }

    /**
     * API: Get chart data.
     */
    public function statsCharts()
    {
        $videosPerDay = Video::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('count(*) as count')
            )
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $downloadsPerDay = VideoDownload::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('count(*) as count')
            )
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return response()->json([
            'videos_per_day' => $videosPerDay,
            'downloads_per_day' => $downloadsPerDay,
        ]);
    }
}
