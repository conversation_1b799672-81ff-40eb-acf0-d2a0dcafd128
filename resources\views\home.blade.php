@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Hero Section -->
    <div class="text-center py-12">
        <h1 class="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
            Welcome to <span class="text-black">StoryGue</span>
        </h1>
        <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
            Discover and share amazing short videos. Download your favorites and explore trending content from creators around the world.
        </p>
        @guest
            <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">
                <div class="rounded-md shadow">
                    <a href="{{ route('register') }}" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-black hover:bg-gray-800 md:py-4 md:text-lg md:px-10">
                        Get Started
                    </a>
                </div>
                <div class="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">
                    <a href="{{ route('login') }}" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-black bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10">
                        Sign In
                    </a>
                </div>
            </div>
        @else
            <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">
                <div class="rounded-md shadow">
                    <a href="{{ route('videos.create') }}" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-black hover:bg-gray-800 md:py-4 md:text-lg md:px-10">
                        Upload Video
                    </a>
                </div>
            </div>
        @endguest
    </div>

    <!-- Videos Grid -->
    @if($videos->count() > 0)
        <div class="py-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Latest Videos</h2>

            <div class="video-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                @foreach($videos as $video)
                    <div class="video-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                        <!-- Video Thumbnail -->
                        <div class="relative aspect-video bg-gray-200">
                            @if($video->thumbnail_path)
                                <img src="{{ Storage::url($video->thumbnail_path) }}"
                                     alt="{{ $video->title }}"
                                     class="w-full h-full object-cover">
                            @else
                                <div class="w-full h-full flex items-center justify-center bg-gray-300">
                                    <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            @endif

                            <!-- Duration overlay -->
                            @if($video->duration)
                                <div class="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                                    {{ $video->formatted_duration }}
                                </div>
                            @endif
                        </div>

                        <!-- Video Info -->
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-900 text-sm mb-2 line-clamp-2">
                                {{ $video->title }}
                            </h3>

                            @if($video->description)
                                <p class="text-gray-600 text-xs mb-3 line-clamp-2">
                                    {{ $video->description }}
                                </p>
                            @endif

                            <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                                <span>by {{ $video->user->name }}</span>
                                <span>{{ $video->created_at->diffForHumans() }}</span>
                            </div>

                            <!-- Stats -->
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-4">
                                <div class="flex items-center space-x-4">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                                        </svg>
                                        {{ $video->likes_count }}
                                    </span>
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                        {{ $video->downloads_count }}
                                    </span>
                                </div>
                                @if($video->file_size)
                                    <span>{{ $video->formatted_file_size }}</span>
                                @endif
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex space-x-2">
                                <button onclick="likeVideo({{ $video->id }})"
                                        id="like-btn-{{ $video->id }}"
                                        class="like-btn flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 text-xs font-medium py-2 px-3 rounded transition-colors duration-200"
                                        data-tooltip="Like this video">
                                    <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                                    </svg>
                                    <span id="like-count-{{ $video->id }}">{{ $video->likes_count }}</span>
                                </button>
                                <a href="{{ route('videos.download', $video) }}"
                                   class="flex-1 bg-black hover:bg-gray-800 text-white text-xs font-medium py-2 px-3 rounded text-center transition-colors duration-200">
                                    <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                    Download
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $videos->links() }}
            </div>
        </div>
    @else
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No videos yet</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by uploading your first video.</p>
            @auth
                <div class="mt-6">
                    <a href="{{ route('videos.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                        </svg>
                        Upload Video
                    </a>
                </div>
            @endauth
        </div>
    @endif
</div>

<!-- Footer -->
<footer class="bg-white border-t border-gray-200 mt-12">
    <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div class="flex justify-center space-x-6">
            <a href="{{ route('terms') }}" class="text-gray-500 hover:text-gray-900 text-sm">Terms of Service</a>
            <a href="{{ route('privacy') }}" class="text-gray-500 hover:text-gray-900 text-sm">Privacy Policy</a>
        </div>
        <div class="mt-4 text-center">
            <p class="text-gray-400 text-sm">&copy; {{ date('Y') }} StoryGue. All rights reserved.</p>
        </div>
    </div>
</footer>

<script>
function likeVideo(videoId) {
    fetch(`/videos/${videoId}/like`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        // Update like count in the UI
        // You can enhance this to show visual feedback
        location.reload(); // Simple reload for now
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
@endsection
