@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- Video Player -->
        <div class="relative bg-black">
            <video controls class="w-full h-auto max-h-96" poster="{{ $video->thumbnail_path ? Storage::url($video->thumbnail_path) : '' }}">
                <source src="{{ Storage::url($video->video_path) }}" type="video/mp4">
                Your browser does not support the video tag.
            </video>
        </div>

        <!-- Video Info -->
        <div class="p-6">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <h1 class="text-2xl font-bold text-gray-900 mb-2">{{ $video->title }}</h1>
                    
                    <div class="flex items-center text-sm text-gray-500 mb-4">
                        <span>by {{ $video->user->name }}</span>
                        <span class="mx-2">•</span>
                        <span>{{ $video->created_at->format('M j, Y') }}</span>
                        @if($video->duration)
                            <span class="mx-2">•</span>
                            <span>{{ $video->formatted_duration }}</span>
                        @endif
                        @if($video->file_size)
                            <span class="mx-2">•</span>
                            <span>{{ $video->formatted_file_size }}</span>
                        @endif
                    </div>

                    @if($video->description)
                        <div class="prose prose-sm max-w-none mb-6">
                            <p class="text-gray-700">{{ $video->description }}</p>
                        </div>
                    @endif
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center space-x-3 ml-6">
                    <button onclick="likeVideo({{ $video->id }})" 
                            id="like-btn-{{ $video->id }}"
                            class="flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md transition-colors duration-200">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                        </svg>
                        <span id="like-count-{{ $video->id }}">{{ $video->likes_count }}</span>
                    </button>

                    <a href="{{ route('videos.download', $video) }}" 
                       class="flex items-center px-4 py-2 bg-black hover:bg-gray-800 text-white rounded-md transition-colors duration-200">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                        Download ({{ $video->downloads_count }})
                    </a>
                </div>
            </div>

            <!-- Stats -->
            <div class="border-t border-gray-200 pt-6 mt-6">
                <div class="grid grid-cols-2 gap-6 sm:grid-cols-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-900">{{ $video->likes_count }}</div>
                        <div class="text-sm text-gray-500">Likes</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-900">{{ $video->downloads_count }}</div>
                        <div class="text-sm text-gray-500">Downloads</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-900">{{ $video->created_at->diffForHumans() }}</div>
                        <div class="text-sm text-gray-500">Uploaded</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-900">
                            @if($video->is_active)
                                <span class="text-green-600">Active</span>
                            @else
                                <span class="text-red-600">Inactive</span>
                            @endif
                        </div>
                        <div class="text-sm text-gray-500">Status</div>
                    </div>
                </div>
            </div>

            <!-- Owner Actions -->
            @if(auth()->check() && (auth()->id() === $video->user_id || auth()->user()->isAdmin()))
                <div class="border-t border-gray-200 pt-6 mt-6">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Video Management</h3>
                        <div class="flex space-x-3">
                            @if(auth()->user()->isAdmin() && auth()->id() !== $video->user_id)
                                <form action="{{ route('admin.videos.toggle-status', $video) }}" method="POST" class="inline">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit" 
                                            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                        @if($video->is_active)
                                            Deactivate
                                        @else
                                            Activate
                                        @endif
                                    </button>
                                </form>
                            @endif

                            <form action="{{ route('videos.destroy', $video) }}" method="POST" class="inline" 
                                  onsubmit="return confirm('Are you sure you want to delete this video? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" 
                                        class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                                    Delete Video
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Back Button -->
    <div class="mt-6">
        <a href="{{ route('home') }}" 
           class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            <svg class="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            Back to Home
        </a>
    </div>
</div>

<script>
function likeVideo(videoId) {
    const likeBtn = document.getElementById(`like-btn-${videoId}`);
    const likeCount = document.getElementById(`like-count-${videoId}`);
    
    // Disable button temporarily
    likeBtn.disabled = true;
    
    fetch(`/videos/${videoId}/like`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        // Update like count
        likeCount.textContent = data.likes_count;
        
        // Update button appearance based on like status
        if (data.liked) {
            likeBtn.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'text-gray-800');
            likeBtn.classList.add('bg-red-100', 'hover:bg-red-200', 'text-red-800');
        } else {
            likeBtn.classList.remove('bg-red-100', 'hover:bg-red-200', 'text-red-800');
            likeBtn.classList.add('bg-gray-100', 'hover:bg-gray-200', 'text-gray-800');
        }
        
        // Re-enable button
        likeBtn.disabled = false;
    })
    .catch(error => {
        console.error('Error:', error);
        likeBtn.disabled = false;
    });
}

// Auto-play video when page loads (optional)
document.addEventListener('DOMContentLoaded', function() {
    const video = document.querySelector('video');
    if (video) {
        // You can uncomment the next line if you want auto-play
        // video.play();
    }
});
</script>
@endsection
