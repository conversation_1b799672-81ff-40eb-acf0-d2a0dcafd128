// StoryGue Custom JavaScript

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize all components
    initializeNotifications();
    initializeVideoCards();
    initializeLazyLoading();
    initializeTooltips();
    initializeKeyboardNavigation();
    initializeProgressBars();
}

// Notification System
function initializeNotifications() {
    // Auto-hide success/error messages after 5 seconds
    const notifications = document.querySelectorAll('.notification, .alert');
    notifications.forEach(notification => {
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    });
}

// Video Card Enhancements
function initializeVideoCards() {
    const videoCards = document.querySelectorAll('.video-card');
    
    videoCards.forEach(card => {
        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.classList.add('shadow-lg');
        });
        
        card.addEventListener('mouseleave', function() {
            this.classList.remove('shadow-lg');
        });
        
        // Add click to play functionality (if video element exists)
        const video = card.querySelector('video');
        if (video) {
            card.addEventListener('click', function(e) {
                if (e.target.tagName !== 'BUTTON' && e.target.tagName !== 'A') {
                    if (video.paused) {
                        video.play();
                    } else {
                        video.pause();
                    }
                }
            });
        }
    });
}

// Lazy Loading for Images and Videos
function initializeLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

// Tooltip System
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(e) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip-popup';
    tooltip.textContent = e.target.dataset.tooltip;
    document.body.appendChild(tooltip);
    
    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
}

function hideTooltip() {
    const tooltip = document.querySelector('.tooltip-popup');
    if (tooltip) {
        tooltip.remove();
    }
}

// Keyboard Navigation
function initializeKeyboardNavigation() {
    document.addEventListener('keydown', function(e) {
        // ESC key to close modals
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
        }
        
        // Enter key to submit forms when focused on submit button
        if (e.key === 'Enter' && e.target.type === 'submit') {
            e.target.click();
        }
    });
}

// Progress Bar Animation
function initializeProgressBars() {
    const progressBars = document.querySelectorAll('.progress-bar');
    
    const progressObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const progressBar = entry.target;
                const width = progressBar.dataset.width || '100%';
                progressBar.style.width = width;
            }
        });
    });
    
    progressBars.forEach(bar => {
        progressObserver.observe(bar);
    });
}

// Like Video Function (Enhanced)
function likeVideo(videoId) {
    const likeBtn = document.getElementById(`like-btn-${videoId}`);
    const likeCount = document.getElementById(`like-count-${videoId}`);
    
    if (!likeBtn || !likeCount) return;
    
    // Add loading state
    likeBtn.disabled = true;
    likeBtn.classList.add('opacity-50');
    
    // Show loading spinner
    const originalContent = likeBtn.innerHTML;
    likeBtn.innerHTML = '<div class="spinner w-4 h-4"></div>';
    
    fetch(`/videos/${videoId}/like`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        // Update like count with animation
        likeCount.textContent = data.likes_count;
        
        // Update button appearance
        if (data.liked) {
            likeBtn.classList.add('liked', 'text-red-500');
            likeBtn.classList.remove('text-gray-500');
            
            // Heart animation
            likeBtn.classList.add('animate-pulse');
            setTimeout(() => {
                likeBtn.classList.remove('animate-pulse');
            }, 600);
        } else {
            likeBtn.classList.remove('liked', 'text-red-500');
            likeBtn.classList.add('text-gray-500');
        }
        
        // Restore button
        likeBtn.innerHTML = originalContent;
        likeBtn.disabled = false;
        likeBtn.classList.remove('opacity-50');
        
        // Show success feedback
        showNotification(data.liked ? 'Video liked!' : 'Like removed', 'success');
    })
    .catch(error => {
        console.error('Error:', error);
        
        // Restore button
        likeBtn.innerHTML = originalContent;
        likeBtn.disabled = false;
        likeBtn.classList.remove('opacity-50');
        
        showNotification('Error updating like status', 'error');
    });
}

// Show Notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification fixed top-4 right-4 px-4 py-2 rounded-md text-white z-50 ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        'bg-blue-500'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// File Upload Enhancement
function initializeFileUpload() {
    const fileInput = document.getElementById('video');
    const uploadArea = document.querySelector('.upload-area');
    
    if (!fileInput || !uploadArea) return;
    
    // Drag and drop functionality
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight(e) {
        uploadArea.classList.add('dragover');
    }
    
    function unhighlight(e) {
        uploadArea.classList.remove('dragover');
    }
    
    uploadArea.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect(files[0]);
        }
    }
    
    // File input change handler
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });
    
    function handleFileSelect(file) {
        // Validate file type
        const allowedTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/wmv'];
        if (!allowedTypes.includes(file.type)) {
            showNotification('Please select a valid video file (MP4, MOV, AVI, WMV)', 'error');
            return;
        }
        
        // Validate file size (50MB)
        const maxSize = 50 * 1024 * 1024;
        if (file.size > maxSize) {
            showNotification('File size must be less than 50MB', 'error');
            return;
        }
        
        // Show file info
        const fileInfo = document.createElement('div');
        fileInfo.className = 'mt-2 text-sm text-gray-600';
        fileInfo.innerHTML = `
            <p><strong>File:</strong> ${file.name}</p>
            <p><strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
            <p><strong>Type:</strong> ${file.type}</p>
        `;
        
        // Replace existing file info
        const existingInfo = uploadArea.querySelector('.file-info');
        if (existingInfo) {
            existingInfo.remove();
        }
        
        fileInfo.classList.add('file-info');
        uploadArea.appendChild(fileInfo);
        
        // Update upload area appearance
        uploadArea.classList.add('border-green-300', 'bg-green-50');
        uploadArea.classList.remove('border-gray-300');
    }
}

// Initialize file upload when on upload page
if (document.getElementById('video')) {
    initializeFileUpload();
}

// Smooth scroll for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Back to top button
function addBackToTopButton() {
    const backToTop = document.createElement('button');
    backToTop.innerHTML = '↑';
    backToTop.className = 'fixed bottom-4 right-4 bg-black text-white p-3 rounded-full shadow-lg opacity-0 transition-opacity duration-300 z-50';
    backToTop.style.display = 'none';
    
    document.body.appendChild(backToTop);
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTop.style.display = 'block';
            setTimeout(() => backToTop.style.opacity = '1', 10);
        } else {
            backToTop.style.opacity = '0';
            setTimeout(() => backToTop.style.display = 'none', 300);
        }
    });
    
    backToTop.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Add back to top button
addBackToTopButton();
